import json
import re
from bs4 import BeautifulSoup
import pyjsparser

class QuizDetector:
    @staticmethod
    def detect_quiz_type(html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        if 'const quizData = {' in content:
            return 'js_quiz'
        elif 'questions.push({' in content:
            return 'mocks_wallah'
        elif 'class="question"' in content and 'class="options"' in content and 'onclick="checkAnswer' in content:
            return 'simple_quiz'
        elif 'question-block' in content and 'data-correct' in content:
            return 'wallah_test'
        else:
            return 'unknown'

class JSQuizExtractor:
    def extract_content_with_html(self, element):
        if element is None:
            return ""
        return "".join([str(content) for content in element.contents]).strip()

    def extract(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        script_match = re.search(r'const quizData = ({.*?}] });', content, re.DOTALL)
        if not script_match:
            raise ValueError("Could not find quizData")

        js_code = f"var quizData = {script_match.group(1)};"
        parsed = pyjsparser.parse(js_code)
        questions_array = parsed['body'][0]['declarations'][0]['init']['properties'][0]['value']['elements']
        
        questions = []
        for question_obj in questions_array:
            question_data = {}
            for prop in question_obj['properties']:
                key = prop['key']['name']
                if key in ['id', 'text', 'reference', 'correctIndex', 'explanation']:
                    question_data[key] = prop['value']['value']
                elif key == 'options':
                    question_data[key] = [elem['value'] for elem in prop['value']['elements']]
            questions.append(question_data)

        title_match = re.search(r'<title>(.*?)</title>', content)
        test_name = title_match.group(1).strip() if title_match else "Mock Test"
        time_match = re.search(r'totalTime:\s*(\d+)', content)
        duration_minutes = (int(time_match.group(1)) if time_match else 900) // 60
        neg_mark_match = re.search(r'negativeMark:\s*([\d.]+)', content)
        negative_marks = float(neg_mark_match.group(1)) if neg_mark_match else 0.25

        total_questions = len(questions)
        formatted_questions = [{
            "type": "mcq_single_correct",
            "question": q['text'],
            "options": q['options'],
            "answer": int(q['correctIndex'] + 1),
            "solution": q['explanation'],
            "positive_marks": 1.0,
            "negative_makrs": negative_marks
        } for q in questions]

        section = {
            "section_name": "Vocabulary",
            "section_questions": total_questions,
            "section_marks": float(total_questions),
            "pre": [],
            "question_list": formatted_questions
        }

        test_data = {
            "name": test_name,
            "duration": duration_minutes,
            "marks": total_questions,
            "total_questions": total_questions,
            "sections": [section],
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }

        return {
            "all": {**test_data, "instructions": {"en": ""}, "languages": {"en": "English"}},
            "en": test_data
        }

class SimpleQuizExtractor:
    def extract_content_with_html(self, element):
        if element is None:
            return ""
        return "".join([str(content) for content in element.contents]).strip()

    def extract(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        title_tag = soup.find('title')
        test_name = title_tag.text.strip() if title_tag else "Mock Test"
        question_divs = soup.find_all('div', {'class': 'question'})
        
        questions = []
        for question_div in question_divs:
            question_text = self.extract_content_with_html(question_div)
            options_div = question_div.find_next_sibling('div', {'class': 'options'})
            if not options_div:
                continue
            
            option_buttons = options_div.find_all('button')
            options = []
            correct_answer = 1
            
            for i, button in enumerate(option_buttons):
                option_content = self.extract_content_with_html(button)
                options.append(option_content)
                onclick = button.get('onclick', '')
                if 'true' in onclick:
                    correct_answer = i + 1
            
            solution_p = options_div.find_next_sibling('p', {'class': 'ai-response'})
            solution_content = ""
            if solution_p:
                solution_content = self.extract_content_with_html(solution_p)
                solution_content = re.sub(r'^🔍\s*<b>AI:</b>\s*', '', solution_content)
                if solution_content.strip() == "None":
                    solution_content = ""
            
            questions.append({
                "type": "mcq_single_correct",
                "question": question_text,
                "options": options,
                "answer": correct_answer,
                "solution": solution_content,
                "positive_marks": 1.0,
                "negative_makrs": 0.0
            })
        
        total_questions = len(questions)
        section = {
            "section_name": "General Knowledge",
            "section_questions": total_questions,
            "section_marks": float(total_questions),
            "pre": [],
            "question_list": questions
        }
        
        test_data = {
            "name": test_name,
            "duration": total_questions,
            "marks": total_questions,
            "total_questions": total_questions,
            "sections": [section],
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }
        
        return {
            "all": {**test_data, "instructions": {"en": ""}, "languages": {"en": "English"}},
            "en": test_data
        }

class WallahTestExtractor:
    def extract_content_with_html(self, element):
        if element is None:
            return ""
        return "".join([str(content) for content in element.contents]).strip()

    def extract(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        soup = BeautifulSoup(content, 'html.parser')
        title_tag = soup.find('title')
        test_name = title_tag.text.strip() if title_tag else "Mock Test"
        total_marks_match = re.search(r'let totalMarks = (\d+);', content)
        total_marks = int(total_marks_match.group(1)) if total_marks_match else 0
        section_tabs = soup.find_all('button', {'class': 'nav-link'})
        section_names = [tab.get_text().strip() for tab in section_tabs]
        sections = []
        total_questions = 0

        for i, section_name in enumerate(section_names):
            section_div = soup.find('div', {'id': f'section{i}'})
            if not section_div:
                continue
            question_blocks = section_div.find_all('div', {'class': 'question-block'})
            question_list = []
            section_marks = 0

            for question_block in question_blocks:
                correct_answer = int(question_block.get('data-correct', 0))
                pos_marks = float(question_block.get('data-pos-marks', 1))
                neg_marks = float(question_block.get('data-neg-marks', 0))
                question_text_div = question_block.find('div', {'class': 'question-text'})
                question_text = self.extract_content_with_html(question_text_div) if question_text_div else ""
                comp_text_div = question_block.find('div', {'class': 'comp-text'})
                comp_text = self.extract_content_with_html(comp_text_div) if comp_text_div else ""
                if comp_text:
                    question_text = comp_text + "<br><br>" + question_text
                option_divs = question_block.find_all('div', {'class': 'option-text'})
                options = [self.extract_content_with_html(option_div) for option_div in option_divs]
                solution_div = question_block.find('div', {'class': 'solution'})
                solution_content = ""
                if solution_div:
                    solution_text_div = solution_div.find('div')
                    if solution_text_div:
                        solution_content = self.extract_content_with_html(solution_text_div)

                question_obj = {
                    "type": "mcq_single_correct",
                    "question": question_text,
                    "options": options,
                    "answer": correct_answer + 1,
                    "solution": solution_content,
                    "positive_marks": pos_marks,
                    "negative_makrs": neg_marks
                }
                question_list.append(question_obj)
                section_marks += pos_marks
                total_questions += 1

            section_obj = {
                "section_name": section_name,
                "section_questions": len(question_list),
                "section_marks": section_marks,
                "pre": [],
                "question_list": question_list
            }
            sections.append(section_obj)

        if total_marks == 0:
            total_marks = sum(section['section_marks'] for section in sections)

        test_data = {
            "name": test_name,
            "duration": 2,
            "marks": total_marks,
            "total_questions": total_questions,
            "sections": sections,
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }

        return {
            "all": {
                "name": test_name,
                "duration": 2,
                "marks": total_marks,
                "total_questions": total_questions,
                "sections": sections,
                "instructions": {"en": ""},
                "languages": {"en": "English"},
                "primary_language": "en"
            },
            "en": test_data
        }

class MocksWallahExtractor:
    def extract(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        soup = BeautifulSoup(content, 'html.parser')

        script_content = None
        for script in soup.find_all('script'):
            if script.string and 'questions.push(' in script.string:
                script_content = script.string
                break
        if not script_content:
            raise ValueError("Could not find questions data")

        test_name = self.extract_test_name(soup, script_content)
        duration = self.extract_duration(script_content)
        questions_data = self.extract_questions(script_content)

        total_questions = len(questions_data)
        total_marks = sum(q.get('pos_marks', 2) for q in questions_data)

        formatted_questions = []
        for q in questions_data:
            formatted_questions.append({
                "type": "mcq_single_correct",
                "question": q.get('question_en', ''),
                "options": q.get('options_en', []),
                "answer": self.convert_answer(q.get('correct', 'a')),
                "solution": q.get('solution_en', ''),
                "positive_marks": q.get('pos_marks', 2.0),
                "negative_makrs": q.get('neg_marks', 0.5)
            })

        section = {
            "section_name": "All Questions",
            "section_questions": total_questions,
            "section_marks": float(total_marks),
            "pre": [],
            "question_list": formatted_questions
        }

        test_data = {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": [section],
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }

        return {
            "all": {**test_data, "instructions": {"en": ""}, "languages": {"en": "English"}},
            "en": test_data
        }

    def extract_test_name(self, soup, script_content):
        title_tag = soup.find('title')
        if title_tag and title_tag.text:
            title_text = title_tag.text.strip()
            if title_text and title_text.lower() != 'test':
                return title_text
        return "Test"

    def extract_duration(self, script_content):
        try:
            lines = script_content.split('\n')
            for line in lines:
                if 'totalTime' in line and '=' in line:
                    numbers = re.findall(r'\d+', line)
                    if numbers:
                        return int(numbers[0])
        except:
            pass
        return 8

    def extract_questions(self, script_content):
        questions_data = []
        lines = script_content.split('\n')
        current_question = ""
        in_question = False
        brace_count = 0

        for line in lines:
            line = line.strip()
            if 'questions.push({' in line:
                in_question = True
                current_question = line
                brace_count = line.count('{') - line.count('}')
            elif in_question:
                current_question += '\n' + line
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0:
                    try:
                        question_obj = self.parse_question(current_question)
                        if question_obj:
                            questions_data.append(question_obj)
                    except:
                        pass
                    in_question = False
                    current_question = ""
                    brace_count = 0
        return questions_data

    def parse_question(self, question_line):
        try:
            clean_line = question_line.strip().rstrip(';')
            ast = pyjsparser.parse(clean_line)

            def walk_ast(node):
                if isinstance(node, dict):
                    if (node.get('type') == 'CallExpression' and
                        node.get('callee', {}).get('property', {}).get('name') == 'push'):
                        arguments = node.get('arguments', [])
                        if arguments and arguments[0].get('type') == 'ObjectExpression':
                            return self.parse_js_object(arguments[0])
                    for value in node.values():
                        if isinstance(value, (dict, list)):
                            result = walk_ast(value)
                            if result:
                                return result
                elif isinstance(node, list):
                    for item in node:
                        result = walk_ast(item)
                        if result:
                            return result
                return None
            return walk_ast(ast)
        except:
            return None

    def parse_js_object(self, obj_node):
        question_data = {
            'question_en': '', 'options_en': [], 'solution_en': '',
            'correct': 'a', 'pos_marks': 2.0, 'neg_marks': 0.5
        }

        for prop in obj_node.get('properties', []):
            key_name = prop.get('key', {}).get('name')
            value_node = prop.get('value', {})

            if key_name in question_data:
                if value_node.get('type') == 'Literal':
                    value = value_node.get('value')
                    if key_name in ['pos_marks', 'neg_marks']:
                        question_data[key_name] = float(value) if value else question_data[key_name]
                    else:
                        question_data[key_name] = value
                elif value_node.get('type') == 'ArrayExpression':
                    elements = []
                    for element in value_node.get('elements', []):
                        if element.get('type') == 'Literal':
                            elements.append(element.get('value'))
                    question_data[key_name] = elements

        return question_data

    def convert_answer(self, correct_letter):
        mapping = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
        return mapping.get(correct_letter.lower(), 1)

def main(html_file_path, output_file=None):
    quiz_type = QuizDetector.detect_quiz_type(html_file_path)
    
    if quiz_type == 'js_quiz':
        extractor = JSQuizExtractor()
    elif quiz_type == 'simple_quiz':
        extractor = SimpleQuizExtractor()
    elif quiz_type == 'wallah_test':
        extractor = WallahTestExtractor()
    elif quiz_type == 'mocks_wallah':
        extractor = MocksWallahExtractor()
    else:
        raise ValueError(f"Unknown quiz type detected for file: {html_file_path}")
    
    result = extractor.extract(html_file_path)
    
    if output_file is None:
        output_file = f"extracted_{quiz_type}_data.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    return result, quiz_type

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python unified_quiz_extractor.py <html_file> [output_file]")
        sys.exit(1)
    
    html_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        result, quiz_type = main(html_file, output_file)
        print(f"Detected quiz type: {quiz_type}")
        print(f"Extracted {result['en']['total_questions']} questions")
        print(f"Test name: {result['en']['name']}")
        print(f"Output saved to: {output_file or f'extracted_{quiz_type}_data.json'}")
    except Exception as e:
        print(f"Error: {e}")
