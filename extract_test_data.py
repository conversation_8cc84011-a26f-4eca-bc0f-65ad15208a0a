import json
from bs4 import BeautifulSoup
import re

class TestExtractor:
    def extract_content_with_html(self, element):
        if element is None:
            return ""
        return "".join([str(content) for content in element.contents]).strip()

    def extract_test_data(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        soup = BeautifulSoup(content, 'html.parser')
        title_tag = soup.find('title')
        test_name = title_tag.text.strip() if title_tag else "Mock Test"
        total_marks_match = re.search(r'let totalMarks = (\d+);', content)
        total_marks = int(total_marks_match.group(1)) if total_marks_match else 0
        section_tabs = soup.find_all('button', {'class': 'nav-link'})
        section_names = [tab.get_text().strip() for tab in section_tabs]
        sections = []
        total_questions = 0

        for i, section_name in enumerate(section_names):
            section_div = soup.find('div', {'id': f'section{i}'})
            if not section_div:
                continue
            question_blocks = section_div.find_all('div', {'class': 'question-block'})
            question_list = []
            section_marks = 0

            for question_block in question_blocks:
                correct_answer = int(question_block.get('data-correct', 0))
                pos_marks = float(question_block.get('data-pos-marks', 1))
                neg_marks = float(question_block.get('data-neg-marks', 0))
                question_text_div = question_block.find('div', {'class': 'question-text'})
                question_text = self.extract_content_with_html(question_text_div) if question_text_div else ""
                comp_text_div = question_block.find('div', {'class': 'comp-text'})
                comp_text = self.extract_content_with_html(comp_text_div) if comp_text_div else ""
                if comp_text:
                    question_text = comp_text + "<br><br>" + question_text
                option_divs = question_block.find_all('div', {'class': 'option-text'})
                options = [self.extract_content_with_html(option_div) for option_div in option_divs]
                solution_div = question_block.find('div', {'class': 'solution'})
                solution_content = ""
                if solution_div:
                    solution_text_div = solution_div.find('div')
                    if solution_text_div:
                        solution_content = self.extract_content_with_html(solution_text_div)

                question_obj = {
                    "type": "mcq_single_correct",
                    "question": question_text,
                    "options": options,
                    "answer": correct_answer + 1,
                    "solution": solution_content,
                    "positive_marks": pos_marks,
                    "negative_makrs": neg_marks
                }
                question_list.append(question_obj)
                section_marks += pos_marks
                total_questions += 1

            section_obj = {
                "section_name": section_name,
                "section_questions": len(question_list),
                "section_marks": section_marks,
                "pre": [],
                "question_list": question_list
            }
            sections.append(section_obj)

        if total_marks == 0:
            total_marks = sum(section['section_marks'] for section in sections)

        test_data = {
            "name": test_name,
            "duration": total_questions,
            "marks": total_marks,
            "total_questions": total_questions,
            "sections": sections,
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }

        return {
            "all": {
                "name": test_name,
                "duration": total_questions,
                "marks": total_marks,
                "total_questions": total_questions,
                "sections": sections,
                "instructions": {"en": ""},
                "languages": {"en": "English"},
                "primary_language": "en"
            },
            "en": test_data
        }

    def extract_and_save(self, html_file, output_file='extracted_test_data.json'):
        test_data = self.extract_test_data(html_file)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        return test_data

extractor = TestExtractor()
test_data = extractor.extract_and_save('VaZnx_mocks_wallah.html')