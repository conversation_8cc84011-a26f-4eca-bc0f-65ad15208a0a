#!/usr/bin/env python3
"""
Script to extract test data from VaZnx_mocks_wallah.html and convert to JSON format
Uses BeautifulSoup4 for HTML parsing as requested
"""

import json
from bs4 import BeautifulSoup
import re

def extract_content_with_html(element):
    """Extract content from element preserving HTML structure"""
    if element is None:
        return ""
    
    # Get all contents including text and HTML
    contents = []
    for content in element.contents:
        if hasattr(content, 'name'):  # It's a tag
            contents.append(str(content))
        else:  # It's text
            contents.append(str(content))
    
    return "".join(contents).strip()

def parse_marks_from_text(marks_text):
    """Parse positive and negative marks from text like '[+1 Mark, -0.25 Mark]'"""
    pos_match = re.search(r'\+(\d+(?:\.\d+)?)', marks_text)
    neg_match = re.search(r'-(\d+(?:\.\d+)?)', marks_text)
    
    pos_marks = float(pos_match.group(1)) if pos_match else 1.0
    neg_marks = float(neg_match.group(1)) if neg_match else 0.0
    
    return pos_marks, neg_marks

def extract_test_data(html_file_path):
    """Extract test data from HTML file"""
    
    with open(html_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Extract test name from title
    title_tag = soup.find('title')
    test_name = title_tag.text.strip() if title_tag else "Mock Test"
    
    # Extract total marks from JavaScript (default to calculated value if not found)
    total_marks_match = re.search(r'let totalMarks = (\d+);', content)
    total_marks = int(total_marks_match.group(1)) if total_marks_match else 0
    
    # Find all sections
    section_tabs = soup.find_all('button', {'class': 'nav-link'})
    section_names = []
    for tab in section_tabs:
        section_name = tab.get_text().strip()
        section_names.append(section_name)
    
    # Find all section content divs
    sections = []
    total_questions = 0
    
    for i, section_name in enumerate(section_names):
        section_div = soup.find('div', {'id': f'section{i}'})
        if not section_div:
            continue
            
        # Find all questions in this section
        question_blocks = section_div.find_all('div', {'class': 'question-block'})
        question_list = []
        section_marks = 0
        
        for question_block in question_blocks:
            # Extract question ID and metadata
            question_id = question_block.get('id', '')
            correct_answer = int(question_block.get('data-correct', 0))
            pos_marks = float(question_block.get('data-pos-marks', 1))
            neg_marks = float(question_block.get('data-neg-marks', 0))
            
            # Extract question text
            question_text_div = question_block.find('div', {'class': 'question-text'})
            question_text = extract_content_with_html(question_text_div) if question_text_div else ""
            
            # Extract comprehension text if present
            comp_text_div = question_block.find('div', {'class': 'comp-text'})
            comp_text = extract_content_with_html(comp_text_div) if comp_text_div else ""
            
            # If there's comprehension text, prepend it to question
            if comp_text:
                question_text = comp_text + "<br><br>" + question_text
            
            # Extract options
            option_divs = question_block.find_all('div', {'class': 'option-text'})
            options = []
            for option_div in option_divs:
                option_content = extract_content_with_html(option_div)
                options.append(option_content)
            
            # Extract solution
            solution_div = question_block.find('div', {'class': 'solution'})
            solution_content = ""
            if solution_div:
                # Find the div inside solution that contains the actual solution text
                solution_text_div = solution_div.find('div')
                if solution_text_div:
                    solution_content = extract_content_with_html(solution_text_div)
            
            # Create question object
            question_obj = {
                "type": "mcq_single_correct",
                "question": question_text,
                "options": options,
                "answer": correct_answer + 1,  # Convert from 0-based to 1-based indexing
                "solution": solution_content,
                "positive_marks": pos_marks,
                "negative_makrs": neg_marks  # Note: keeping the typo from example
            }
            
            question_list.append(question_obj)
            section_marks += pos_marks
            total_questions += 1
        
        # Create section object
        section_obj = {
            "section_name": section_name,
            "section_questions": len(question_list),
            "section_marks": section_marks,
            "pre": [],
            "question_list": question_list
        }
        
        sections.append(section_obj)
    
    # Calculate total marks if not found in JavaScript
    if total_marks == 0:
        total_marks = sum(section['section_marks'] for section in sections)
    
    # Create the final structure for single language (en as default)
    test_data = {
        "name": test_name,
        "duration": 2,  # Default duration, adjust as needed
        "marks": total_marks,
        "total_questions": total_questions,
        "sections": sections,
        "instructions": "",
        "languages": "English",
        "primary_language": "en"
    }
    
    # Create the complete output structure
    output = {
        "all": {
            "name": test_name,
            "duration": 2,
            "marks": total_marks,
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {
                "en": ""
            },
            "languages": {
                "en": "English"
            },
            "primary_language": "en"
        },
        "en": test_data
    }
    
    return output

def main():
    """Main function to extract and save test data"""
    html_file = 'VaZnx_mocks_wallah.html'
    output_file = 'extracted_test_data.json'
    
    try:
        print(f"Extracting test data from {html_file}...")
        test_data = extract_test_data(html_file)
        
        print(f"Extracted {test_data['en']['total_questions']} questions from {len(test_data['en']['sections'])} sections")
        print(f"Total marks: {test_data['en']['marks']}")
        
        # Save to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"Test data saved to {output_file}")
        
        # Print summary
        print("\nSection Summary:")
        for section in test_data['en']['sections']:
            print(f"- {section['section_name']}: {section['section_questions']} questions, {section['section_marks']} marks")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
