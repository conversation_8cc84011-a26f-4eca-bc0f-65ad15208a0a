import json
import re
from bs4 import BeautifulSoup
import pyj<PERSON><PERSON>er

def extract_test_data(html_file_path):
    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    soup = BeautifulSoup(content, 'html.parser')
    script_content = None
    for script in soup.find_all('script'):
        if script.string and 'questions.push(' in script.string:
            script_content = script.string
            break
    if not script_content:
        raise ValueError("Could not find questions data in HTML")
    test_name = extract_test_name(soup, script_content)
    duration = extract_duration_from_script(script_content)
    sections_info = extract_sections_from_script(script_content)
    questions_data = extract_questions_from_script(script_content)
    organized_sections = organize_questions_by_sections(questions_data, sections_info)
    total_questions = len(questions_data)
    total_marks = sum(q['pos_marks'] for q in questions_data)
    sections = build_sections_structure(organized_sections)
    result = {
        "all": {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {"en": "", "hi": ""},
            "languages": {"en": "English", "hi": "Hindi"},
            "primary_language": "en"
        }
    }
    result["en"] = create_language_version(result["all"], "en")
    result["hi"] = create_language_version(result["all"], "hi")
    return result


def extract_test_name(soup, script_content):
    title_tag = soup.find('title')
    if title_tag and title_tag.text:
        title_text = title_tag.text.strip()
        if title_text and title_text.lower() != 'test':
            return title_text
    lines = script_content.split('\n')
    for line in lines:
        line = line.strip()
        for var_name in ['testName', 'examName', 'testTitle']:
            if f'{var_name}' in line and '=' in line and '"' in line:
                try:
                    quote_start = line.find('"') + 1
                    quote_end = line.find('"', quote_start)
                    if quote_start > 0 and quote_end > quote_start:
                        name = line[quote_start:quote_end]
                        if len(name) > 3 and not name.startswith('<'):
                            return name
                except:
                    continue
    for selector in ['.test-title', '.exam-title', '#test-name', '#exam-name']:
        try:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                if text and not text.startswith('<') and 'result' not in text.lower():
                    return text
        except:
            continue
    for tag in ['h1', 'h2']:
        elements = soup.find_all(tag)
        for element in elements:
            text = element.get_text().strip()
            if (text and 'test' in text.lower() and 'result' not in text.lower()
                and len(text) < 100 and not text.startswith('<')):
                return text
    return "Test"


def extract_duration_from_script(script_content):
    try:
        lines = script_content.split('\n')
        for line in lines:
            if 'totalTime' in line and '=' in line:
                clean_line = line.strip().rstrip(';')
                if '*' in clean_line:
                    try:
                        ast = pyjsparser.parse(clean_line)
                        def walk_ast(node):
                            if isinstance(node, dict):
                                if (node.get('type') == 'VariableDeclarator' and
                                    node.get('id', {}).get('name') == 'totalTime'):
                                    init_node = node.get('init', {})
                                    if init_node.get('type') == 'BinaryExpression' and init_node.get('operator') == '*':
                                        left = init_node.get('left', {})
                                        if left.get('type') == 'Literal':
                                            return left.get('value', 8)
                                for key, value in node.items():
                                    if isinstance(value, (dict, list)):
                                        result = walk_ast(value)
                                        if result is not None:
                                            return result
                            elif isinstance(node, list):
                                for item in node:
                                    result = walk_ast(item)
                                    if result is not None:
                                        return result
                            return None
                        result = walk_ast(ast)
                        if result is not None:
                            return result
                    except:
                        continue
        return 8
    except Exception as e:
        return 8


def extract_sections_from_script(script_content):
    sections_info = {}
    try:
        lines = script_content.split('\n')
        for line in lines:
            if 'sections[' in line and 'name:' in line:
                clean_line = line.strip().rstrip(';')
                try:
                    ast = pyjsparser.parse(clean_line)
                    def walk_ast(node):
                        if isinstance(node, dict):
                            if node.get('type') == 'AssignmentExpression':
                                left = node.get('left', {})
                                right = node.get('right', {})
                                if (left.get('type') == 'MemberExpression' and
                                    left.get('object', {}).get('name') == 'sections' and
                                    left.get('computed') == True and
                                    right.get('type') == 'ObjectExpression'):
                                    section_id = left.get('property', {}).get('value')
                                    for prop in right.get('properties', []):
                                        key = prop.get('key', {})
                                        key_name = key.get('name') or key.get('value')
                                        if (key_name == 'name' and
                                            prop.get('value', {}).get('type') == 'Literal'):
                                            section_name = prop.get('value', {}).get('value')
                                            if section_id and section_name:
                                                sections_info[section_id] = {
                                                    'name': section_name,
                                                    'questions': []
                                                }
                                                return True
                            for key, value in node.items():
                                if isinstance(value, (dict, list)):
                                    if walk_ast(value):
                                        return True
                        elif isinstance(node, list):
                            for item in node:
                                if walk_ast(item):
                                    return True
                        return False
                    walk_ast(ast)
                except:
                    continue
        if not sections_info:
            sections_info = extract_sections_fallback(script_content)
    except Exception as e:
        sections_info = extract_sections_fallback(script_content)
    return sections_info


def extract_sections_fallback(script_content):
    sections_info = {}
    lines = script_content.split('\n')
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if 'sections[' in line and '=' in line and '{' in line:
            try:
                start_bracket = line.find('sections["') + 10
                end_bracket = line.find('"]', start_bracket)
                if start_bracket > 9 and end_bracket > start_bracket:
                    section_id = line[start_bracket:end_bracket]
                    section_name = None
                    for j in range(i, min(i + 5, len(lines))):
                        check_line = lines[j].strip()
                        if 'name:' in check_line:
                            name_start = check_line.find('name:') + 5
                            if name_start > 4:
                                name_part = check_line[name_start:].strip()
                                if name_part.startswith('"'):
                                    name_end = name_part.find('"', 1)
                                    if name_end > 0:
                                        section_name = name_part[1:name_end]
                                        break
                    if section_name:
                        sections_info[section_id] = {
                            'name': section_name,
                            'questions': []
                        }
            except Exception as e:
                pass
        i += 1
    return sections_info


def extract_questions_from_script(script_content):
    questions_data = []
    try:
        lines = script_content.split('\n')
        current_question = ""
        in_question = False
        brace_count = 0
        for line in lines:
            line = line.strip()
            if 'questions.push({' in line:
                in_question = True
                current_question = line
                brace_count = line.count('{') - line.count('}')
            elif in_question:
                current_question += '\n' + line
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0:
                    try:
                        question_obj = parse_question_line(current_question)
                        if question_obj:
                            questions_data.append(question_obj)
                    except Exception as e:
                        pass
                    in_question = False
                    current_question = ""
                    brace_count = 0
    except Exception as e:
        pass
    return questions_data


def parse_question_line(question_line):
    try:
        clean_line = question_line.strip().rstrip(';')
        ast = pyjsparser.parse(clean_line)
        def walk_ast(node):
            if isinstance(node, dict):
                if (node.get('type') == 'CallExpression' and
                    node.get('callee', {}).get('type') == 'MemberExpression' and
                    node.get('callee', {}).get('object', {}).get('name') == 'questions' and
                    node.get('callee', {}).get('property', {}).get('name') == 'push'):
                    arguments = node.get('arguments', [])
                    if arguments and arguments[0].get('type') == 'ObjectExpression':
                        return parse_js_object_from_ast(arguments[0])
                for key, value in node.items():
                    if isinstance(value, (dict, list)):
                        result = walk_ast(value)
                        if result:
                            return result
            elif isinstance(node, list):
                for item in node:
                    result = walk_ast(item)
                    if result:
                        return result
            return None
        return walk_ast(ast)
    except Exception as e:
        return None


def parse_js_object_from_ast(obj_node):
    def extract_value_from_ast(value_node):
        if not value_node:
            return None
        node_type = value_node.get('type')
        if node_type == 'Literal':
            return value_node.get('value')
        elif node_type == 'ArrayExpression':
            elements = []
            for element in value_node.get('elements', []):
                if element.get('type') == 'Literal':
                    elements.append(element.get('value'))
            return elements
        elif node_type == 'Identifier':
            return value_node.get('name')
        return None
    try:
        question_data = {
            'id': '', 'section': '', 'question_en': '', 'question_hi': '',
            'options_en': [], 'options_hi': [], 'solution_en': '', 'solution_hi': '',
            'correct': '', 'pos_marks': 2.0, 'neg_marks': 0.5
        }
        for prop in obj_node.get('properties', []):
            key_node = prop.get('key', {})
            value_node = prop.get('value', {})
            if key_node.get('type') == 'Identifier':
                key_name = key_node.get('name')
                value = extract_value_from_ast(value_node)
                if key_name in question_data and value is not None:
                    if key_name in ['pos_marks', 'neg_marks']:
                        question_data[key_name] = float(value) if value else question_data[key_name]
                    else:
                        question_data[key_name] = value
        for field_name in ['options_en', 'options_hi']:
            if isinstance(question_data[field_name], list):
                try:
                    json_str = json.dumps(question_data[field_name])
                    question_data[field_name] = json.loads(json_str)
                except:
                    pass
        return question_data
    except Exception as e:
        return None





def organize_questions_by_sections(questions_data, sections_info):
    for question in questions_data:
        section_id = question['section']
        if section_id in sections_info:
            sections_info[section_id]['questions'].append(question)
    return sections_info

def build_sections_structure(organized_sections):
    sections = []

    for section_id, section_data in organized_sections.items():
        section_questions = section_data['questions']
        section = {
            "section_name": section_data['name'],
            "section_questions": len(section_questions),
            "section_marks": sum(q['pos_marks'] for q in section_questions),
            "pre": [],
            "question_list": []
        }
        for q in section_questions:
            question_item = {
                "type": "mcq_single_correct",
                "question": {"en": q['question_en'], "hi": q['question_hi']},
                "options": {"en": q['options_en'], "hi": q['options_hi']},
                "answer": convert_answer_to_index(q['correct']),
                "solution": {"en": q['solution_en'], "hi": q['solution_hi']},
                "positive_marks": q['pos_marks'],
                "negative_makrs": q['neg_marks']
            }
            section['question_list'].append(question_item)
        sections.append(section)
    return sections

def convert_answer_to_index(correct_letter):
    mapping = {'a': 1, 'b': 2, 'c': 3, 'd': 4}
    return mapping.get(correct_letter.lower(), 1)


def create_language_version(all_data, lang):
    lang_data = {
        "name": all_data["name"], "duration": all_data["duration"], "marks": all_data["marks"],
        "total_questions": all_data["total_questions"], "sections": [],
        "instructions": all_data["languages"][lang] if lang in all_data["languages"] else "",
        "languages": all_data["languages"][lang] if lang in all_data["languages"] else lang.title(),
        "primary_language": lang
    }
    for section in all_data["sections"]:
        lang_section = {
            "section_name": section["section_name"], "section_questions": section["section_questions"],
            "section_marks": section["section_marks"], "pre": section["pre"], "question_list": []
        }
        for question in section["question_list"]:
            lang_question = {
                "type": question["type"],
                "question": question["question"][lang] if lang in question["question"] else question["question"]["en"],
                "options": question["options"][lang] if lang in question["options"] else question["options"]["en"],
                "answer": question["answer"],
                "solution": question["solution"][lang] if lang in question["solution"] else question["solution"]["en"],
                "positive_marks": question["positive_marks"], "negative_makrs": question["negative_makrs"]
            }
            lang_section["question_list"].append(lang_question)
        lang_data["sections"].append(lang_section)
    return lang_data


def extract_wallah_test(input_file, output_file='extracted_test_data.json'):
    test_data = extract_test_data(input_file)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    return test_data

if __name__ == "__main__":
    result = extract_wallah_test('mocks_wallah_40c7422e.html')
    print(f"Extracted {result['all']['total_questions']} questions, {len(result['all']['sections'])} sections")
