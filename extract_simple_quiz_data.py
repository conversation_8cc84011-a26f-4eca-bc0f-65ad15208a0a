import json
from bs4 import BeautifulSoup
import re

class SimpleQuizExtractor:
    def extract_content_with_html(self, element):
        if element is None:
            return ""
        return "".join([str(content) for content in element.contents]).strip()

    def extract_quiz_data(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # Extract test name from title
        title_tag = soup.find('title')
        test_name = title_tag.text.strip() if title_tag else "Mock Test"
        
        # Find all question divs
        question_divs = soup.find_all('div', {'class': 'question'})
        
        questions = []
        for question_div in question_divs:
            question_text = self.extract_content_with_html(question_div)
            
            # Find the corresponding options div (next sibling)
            options_div = question_div.find_next_sibling('div', {'class': 'options'})
            if not options_div:
                continue
            
            # Extract options from button elements
            option_buttons = options_div.find_all('button')
            options = []
            correct_answer = 1  # Default to first option
            
            for i, button in enumerate(option_buttons):
                option_content = self.extract_content_with_html(button)
                options.append(option_content)
                
                # Check if this is the correct answer by looking at onclick attribute
                onclick = button.get('onclick', '')
                if 'true' in onclick:
                    correct_answer = i + 1  # Convert to 1-based indexing
            
            # Find the corresponding solution (ai-response)
            solution_p = options_div.find_next_sibling('p', {'class': 'ai-response'})
            solution_content = ""
            if solution_p:
                solution_content = self.extract_content_with_html(solution_p)
                # Remove the "🔍 AI: " prefix if present
                solution_content = re.sub(r'^🔍\s*<b>AI:</b>\s*', '', solution_content)
                if solution_content.strip() == "None":
                    solution_content = ""
            
            question_obj = {
                "type": "mcq_single_correct",
                "question": question_text,
                "options": options,
                "answer": correct_answer,
                "solution": solution_content,
                "positive_marks": 1.0,
                "negative_makrs": 0.0  # Keep the typo from example
            }
            
            questions.append(question_obj)
        
        total_questions = len(questions)
        
        # Create single section
        section = {
            "section_name": "General Knowledge",
            "section_questions": total_questions,
            "section_marks": float(total_questions),
            "pre": [],
            "question_list": questions
        }
        
        # Create the final structure for single language
        test_data = {
            "name": test_name,
            "duration": total_questions,  # Duration equals total questions as requested
            "marks": total_questions,     # Marks equals total questions as requested
            "total_questions": total_questions,
            "sections": [section],
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }
        
        return {
            "all": {
                "name": test_name,
                "duration": total_questions,
                "marks": total_questions,
                "total_questions": total_questions,
                "sections": [section],
                "instructions": {"en": ""},
                "languages": {"en": "English"},
                "primary_language": "en"
            },
            "en": test_data
        }
    
    def extract_and_save(self, html_file, output_file='extracted_simple_quiz_data.json'):
        quiz_data = self.extract_quiz_data(html_file)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(quiz_data, f, ensure_ascii=False, indent=2)
        return quiz_data

# Usage
if __name__ == "__main__":
    extractor = SimpleQuizExtractor()
    try:
        quiz_data = extractor.extract_and_save('6693636856_VIKAS_SIR_TESTING_MIX_GK_GS_CURRENT_BPSC_YODDHA_MARCH.html')
        print(f"Successfully extracted {quiz_data['en']['total_questions']} questions")
        print(f"Test name: {quiz_data['en']['name']}")
        print(f"Duration: {quiz_data['en']['duration']} minutes")
        print(f"Total marks: {quiz_data['en']['marks']}")
        print(f"Section: {quiz_data['en']['sections'][0]['section_name']}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
