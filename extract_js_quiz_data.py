import json
import re
import pyjsparser

class JSQuizExtractor:
    def extract_js_quiz_data(self, html_file_path):
        with open(html_file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        script_match = re.search(r'const quizData = ({.*?}] });', content, re.DOTALL)
        if not script_match:
            raise ValueError("Could not find quizData in the HTML file")

        js_code = f"var quizData = {script_match.group(1)};"
        parsed = pyjsparser.parse(js_code)
        quiz_data = self._extract_quiz_from_ast(parsed)

        title_match = re.search(r'<title>(.*?)</title>', content)
        test_name = title_match.group(1).strip() if title_match else "Mock Test"

        time_match = re.search(r'totalTime:\s*(\d+)', content)
        duration_minutes = (int(time_match.group(1)) if time_match else 900) // 60

        neg_mark_match = re.search(r'negativeMark:\s*([\d.]+)', content)
        negative_marks = float(neg_mark_match.group(1)) if neg_mark_match else 0.25

        return self._format_quiz_data(quiz_data, test_name, duration_minutes, negative_marks)
    
    def _extract_quiz_from_ast(self, ast):
        questions_array = ast['body'][0]['declarations'][0]['init']['properties'][0]['value']['elements']
        questions = []
        for question_obj in questions_array:
            question_data = {}
            for prop in question_obj['properties']:
                key = prop['key']['name']
                if key in ['id', 'text', 'reference', 'correctIndex', 'explanation']:
                    question_data[key] = prop['value']['value']
                elif key == 'options':
                    question_data[key] = [elem['value'] for elem in prop['value']['elements']]
            questions.append(question_data)
        return questions
    
    def _format_quiz_data(self, questions, test_name, duration, negative_marks):
        total_questions = len(questions)
        formatted_questions = [{
            "type": "mcq_single_correct",
            "question": q['text'],
            "options": q['options'],
            "answer": int(q['correctIndex'] + 1),
            "solution": q['explanation'],
            "positive_marks": 1.0,
            "negative_makrs": negative_marks
        } for q in questions]

        section = {
            "section_name": "All",
            "section_questions": total_questions,
            "section_marks": float(total_questions),
            "pre": [],
            "question_list": formatted_questions
        }

        test_data = {
            "name": test_name,
            "duration": duration,
            "marks": total_questions,
            "total_questions": total_questions,
            "sections": [section],
            "instructions": "",
            "languages": "English",
            "primary_language": "en"
        }

        return {
            "all": {**test_data, "instructions": {"en": ""}, "languages": {"en": "English"}},
            "en": test_data
        }
    
    def extract_and_save(self, html_file, output_file='extracted_js_quiz_data.json'):
        quiz_data = self.extract_js_quiz_data(html_file)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(quiz_data, f, ensure_ascii=False, indent=2)
        return quiz_data

# Usage
if __name__ == "__main__":
    extractor = JSQuizExtractor()
    try:
        quiz_data = extractor.extract_and_save('288ee02bd8a646dba250417d637b9766.html')
        print(f"Successfully extracted {quiz_data['en']['total_questions']} questions")
        print(f"Test name: {quiz_data['en']['name']}")
        print(f"Duration: {quiz_data['en']['duration']} minutes")
        print(f"Total marks: {quiz_data['en']['marks']}")
    except Exception as e:
        print(f"Error: {e}")
