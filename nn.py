import json
import re
from bs4 import BeautifulSoup
import pyjsparser

def extract_wallah_test(html_file_path, output_file='extracted_test_data.json'):
    with open(html_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    soup = BeautifulSoup(content, 'html.parser')
    script_content = None
    for script in soup.find_all('script'):
        if script.string and 'questions.push(' in script.string:
            script_content = script.string
            break
    if not script_content:
        raise ValueError("Could not find questions data in HTML")
    
    def extract_test_name(soup, script_content):
        title_tag = soup.find('title')
        if title_tag and title_tag.text:
            title_text = title_tag.text.strip()
            if title_text and title_text.lower() != 'test':
                return title_text
        return "Test"
    
    def extract_duration_from_script(script_content):
        try:
            lines = script_content.split('\n')
            for line in lines:
                if 'totalTime' in line and '=' in line and '*' in line:
                    try:
                        ast = pyjsparser.parse(line.strip().rstrip(';'))
                        def walk_ast(node):
                            if isinstance(node, dict):
                                if (node.get('type') == 'VariableDeclarator' and node.get('id', {}).get('name') == 'totalTime'):
                                    init_node = node.get('init', {})
                                    if init_node.get('type') == 'BinaryExpression' and init_node.get('operator') == '*':
                                        left = init_node.get('left', {})
                                        if left.get('type') == 'Literal':
                                            return left.get('value', 8)
                                for key, value in node.items():
                                    if isinstance(value, (dict, list)):
                                        result = walk_ast(value)
                                        if result is not None:
                                            return result
                            elif isinstance(node, list):
                                for item in node:
                                    result = walk_ast(item)
                                    if result is not None:
                                        return result
                            return None
                        result = walk_ast(ast)
                        if result is not None:
                            return result
                    except:
                        continue
            return 8
        except:
            return 8
    
    def extract_sections_fallback(script_content):
        sections_info = {}
        lines = script_content.split('\n')
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if 'sections[' in line and '=' in line and '{' in line:
                try:
                    start_bracket = line.find('sections["') + 10
                    end_bracket = line.find('"]', start_bracket)
                    if start_bracket > 9 and end_bracket > start_bracket:
                        section_id = line[start_bracket:end_bracket]
                        section_name = None
                        for j in range(i, min(i + 5, len(lines))):
                            check_line = lines[j].strip()
                            if 'name:' in check_line:
                                name_start = check_line.find('name:') + 5
                                if name_start > 4:
                                    name_part = check_line[name_start:].strip()
                                    if name_part.startswith('"'):
                                        name_end = name_part.find('"', 1)
                                        if name_end > 0:
                                            section_name = name_part[1:name_end]
                                            break
                        if section_name:
                            sections_info[section_id] = {'name': section_name, 'questions': []}
                except:
                    pass
            i += 1
        return sections_info
    
    def extract_questions_from_script(script_content):
        questions_data = []
        try:
            lines = script_content.split('\n')
            current_question = ""
            in_question = False
            brace_count = 0
            for line in lines:
                line = line.strip()
                if 'questions.push({' in line:
                    in_question = True
                    current_question = line
                    brace_count = line.count('{') - line.count('}')
                elif in_question:
                    current_question += '\n' + line
                    brace_count += line.count('{') - line.count('}')
                    if brace_count <= 0:
                        try:
                            clean_line = current_question.strip().rstrip(';')
                            ast = pyjsparser.parse(clean_line)
                            def walk_ast(node):
                                if isinstance(node, dict):
                                    if (node.get('type') == 'CallExpression' and node.get('callee', {}).get('object', {}).get('name') == 'questions'):
                                        arguments = node.get('arguments', [])
                                        if arguments and arguments[0].get('type') == 'ObjectExpression':
                                            obj_node = arguments[0]
                                            question_data = {'id': '', 'section': '', 'question_en': '', 'question_hi': '', 'options_en': [], 'options_hi': [], 'solution_en': '', 'solution_hi': '', 'correct': '', 'pos_marks': 2.0, 'neg_marks': 0.5}
                                            for prop in obj_node.get('properties', []):
                                                key_node = prop.get('key', {})
                                                value_node = prop.get('value', {})
                                                if key_node.get('type') == 'Identifier':
                                                    key_name = key_node.get('name')
                                                    if value_node.get('type') == 'Literal':
                                                        value = value_node.get('value')
                                                    elif value_node.get('type') == 'ArrayExpression':
                                                        value = [elem.get('value') for elem in value_node.get('elements', []) if elem.get('type') == 'Literal']
                                                    else:
                                                        value = None
                                                    if key_name in question_data and value is not None:
                                                        if key_name in ['pos_marks', 'neg_marks']:
                                                            question_data[key_name] = float(value) if value else question_data[key_name]
                                                        else:
                                                            question_data[key_name] = value
                                            for field_name in ['options_en', 'options_hi']:
                                                if isinstance(question_data[field_name], list):
                                                    try:
                                                        json_str = json.dumps(question_data[field_name])
                                                        question_data[field_name] = json.loads(json_str)
                                                    except:
                                                        pass
                                            return question_data
                                    for key, value in node.items():
                                        if isinstance(value, (dict, list)):
                                            result = walk_ast(value)
                                            if result:
                                                return result
                                elif isinstance(node, list):
                                    for item in node:
                                        result = walk_ast(item)
                                        if result:
                                            return result
                                return None
                            question_obj = walk_ast(ast)
                            if question_obj:
                                questions_data.append(question_obj)
                        except:
                            pass
                        in_question = False
                        current_question = ""
                        brace_count = 0
        except:
            pass
        return questions_data
    
    test_name = extract_test_name(soup, script_content)
    duration = extract_duration_from_script(script_content)
    sections_info = extract_sections_fallback(script_content)
    questions_data = extract_questions_from_script(script_content)
    
    for question in questions_data:
        section_id = question['section']
        if section_id in sections_info:
            sections_info[section_id]['questions'].append(question)
    
    sections = []
    for section_id, section_data in sections_info.items():
        section_questions = section_data['questions']
        section = {
            "section_name": section_data['name'],
            "section_questions": len(section_questions),
            "section_marks": sum(q['pos_marks'] for q in section_questions),
            "pre": [],
            "question_list": []
        }
        for q in section_questions:
            question_item = {
                "type": "mcq_single_correct",
                "question": {"en": q['question_en'], "hi": q['question_hi']},
                "options": {"en": q['options_en'], "hi": q['options_hi']},
                "answer": {'a': 1, 'b': 2, 'c': 3, 'd': 4}.get(q['correct'].lower(), 1),
                "solution": {"en": q['solution_en'], "hi": q['solution_hi']},
                "positive_marks": q['pos_marks'],
                "negative_makrs": q['neg_marks']
            }
            section['question_list'].append(question_item)
        sections.append(section)
    
    total_questions = len(questions_data)
    total_marks = sum(q['pos_marks'] for q in questions_data)
    
    result = {
        "all": {
            "name": test_name,
            "duration": duration,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {"en": "", "hi": ""},
            "languages": {"en": "English", "hi": "Hindi"},
            "primary_language": "en"
        }
    }
    
    def create_lang_version(all_data, lang):
        lang_data = {"name": all_data["name"], "duration": all_data["duration"], "marks": all_data["marks"], "total_questions": all_data["total_questions"], "sections": [], "instructions": "", "languages": lang.title(), "primary_language": lang}
        for section in all_data["sections"]:
            lang_section = {"section_name": section["section_name"], "section_questions": section["section_questions"], "section_marks": section["section_marks"], "pre": section["pre"], "question_list": []}
            for question in section["question_list"]:
                lang_question = {"type": question["type"], "question": question["question"][lang] if lang in question["question"] else question["question"]["en"], "options": question["options"][lang] if lang in question["options"] else question["options"]["en"], "answer": question["answer"], "solution": question["solution"][lang] if lang in question["solution"] else question["solution"]["en"], "positive_marks": question["positive_marks"], "negative_makrs": question["negative_makrs"]}
                lang_section["question_list"].append(lang_question)
            lang_data["sections"].append(lang_section)
        return lang_data
    
    result["en"] = create_lang_version(result["all"], "en")
    result["hi"] = create_lang_version(result["all"], "hi")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    return result

extract_wallah_test('mocks_wallah_40c7422e.html')